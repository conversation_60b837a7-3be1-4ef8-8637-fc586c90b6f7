part of '_index.dart';

// import 'package:flutter_dotenv/flutter_dotenv.dart';

const tokenKey = "user_token";

final httpProvider = Provider((ref) => HttpProvider(ref));

class HttpProvider {
  late Dio http;
  Ref ref;

  HttpProvider(this.ref)
      : http = Dio(
          BaseOptions(baseUrl: dotenv.env['BASE_URL']!),
        ) {
    init();
  }

  Future<void> init([SharedPreferences? p]) async {
    final pref = p ?? await SharedPreferences.getInstance();
    final token = pref.getString(tokenKey);

    if (token != null) {
      http.options.headers["Authorization"] = "Bearer $token";
    }
  }

  Future<bool> checkToken() async {
    try {
      final pref = await SharedPreferences.getInstance();
      final token = pref.getString(tokenKey);
      print("token == $token");
      if (token != null) {
        // Check if token is expired
        if (await isTokenExpired(token)) {
          print("Token is expired, removing it");
          await removeToken();
          return false;
        }
        await init(pref);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<bool> isTokenExpired(String token) async {
    try {
      // Decode JWT token to check expiry
      final parts = token.split('.');
      if (parts.length != 3) return true;

      final payload = parts[1];
      // Add padding if needed
      final normalizedPayload =
          payload.padRight((payload.length + 3) ~/ 4 * 4, '=');

      final decoded = utf8.decode(base64Url.decode(normalizedPayload));
      final Map<String, dynamic> payloadMap = json.decode(decoded);

      final exp = payloadMap['exp'];
      if (exp == null) return true;

      final expiryDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      final now = DateTime.now();

      // Check if token expires within the next 5 minutes
      final isExpiringSoon =
          expiryDate.isBefore(now.add(const Duration(minutes: 5)));

      print(
          "Token expiry: $expiryDate, Current time: $now, Expiring soon: $isExpiringSoon");

      return isExpiringSoon;
    } catch (e) {
      print("Error checking token expiry: $e");
      return true; // Assume expired if we can't parse
    }
  }

  Future removeToken() async {
    final pref = await SharedPreferences.getInstance();
    await pref.remove(tokenKey);
  }

  Future setToken(String token) async {
    print("setting token == $token");
    final pref = await SharedPreferences.getInstance();
    var b = await pref.setString(tokenKey, token);
    print("setting token $b");
    http.options.headers["Authorization"] = "Bearer $token";
  }
}
