part of '_index.dart';

final recommenderProvider =
    ChangeNotifierProvider((ref) => RecommenderProvider(ref));

class RecommenderProvider extends ChangeNotifier {
  Ref ref;

  List<String> userSkills = [];
  List<String> missingSkills = [];
  bool isLoading = false;
  String? error;

  Dio get http => ref.read(httpProvider).http;
  RecommenderProvider(this.ref) {
    init();
  }

  void init() {
    http.interceptors.add(InterceptorsWrapper(
      onResponse: (res, handler) async {
        if (res.statusCode == 401) {
          print("Unauthorized");
          // await logout();
        }

        return handler.next(res);
      },
    ));
  }

  Future loadRecommendations() async {
    isLoading = true;
    error = null;
    notifyListeners();

    try {
      final resp = await cather(() => http.get('/user/recommend_skills'));

      if (resp.success && resp.result != null) {
        final data = resp.result;

        // Extract user_skills and missing_skills from the response
        userSkills = List<String>.from(data['user_skills'] ?? []);
        missingSkills = List<String>.from(data['missing_skills'] ?? []);

        print('User Skills: $userSkills');
        print('Missing Skills: $missingSkills');

        isLoading = false;
        notifyListeners();
        return resp;
      } else {
        error = resp.error ?? 'Failed to load recommendations';
        isLoading = false;
        notifyListeners();
        throw Exception("failed");
      }
    } catch (err) {
      error = 'Error loading recommendations: $err';
      isLoading = false;
      notifyListeners();
      throw err;
    }
  }
}
