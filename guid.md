# Flutter Job Matching App Development Flow

## Project Overview

Build a Flutter app that matches users with jobs based on their CV and preferences, shows skill gaps, and recommends learning resources.

**Backend:** FastAPI app in `skill_sage` folder  
**Frontend:** Flutter app in `Skill-Sage` folder  
**Sharing:** Use `share_plus` package for all share functionality

## API Endpoints to Integrate

1. `GET /user/me/recommendations?min_match_score=40` - Get job recommendations
2. `GET /user/extract_skills_from_job_recommendations` - Extract skills from jobs
3. `GET /user/detailed_match_analysis/job/{id}/?include_improvement_plan=true&include_similar_items=true` - Job details with improvement plan
4. `GET /youtube/videos/?skill_type=missing` (or matched/all) - Get learning videos
5. `GET /user/preferences` & `PATCH /user/preferences` - User preferences management
6. `GET /user` - Get user details (existing API) - show the llm_insights in user settings page
7. `POST /user/refresh_all_matches/` - Refresh matches cache (use with refresh icon on account settings)

### API Response Examples

#### User Details Response (`GET /user`)

**Note:** User skills can be long - show first five, then "View More" button to toggle showing all skills

```json
{
  "success": true,
  "result": {
    "id": 2,
    "email": "<EMAIL>",
    "role": "ADMIN",
    "profile_id": 2,
    "updated": null,
    "name": "Emmanul",
    "profile_image": null,
    "created": "2025-07-03T14:04:40.895002",
    "education": [],
    "experience": [],
    "profile": {
      "portfolio": null,
      "location": null,
      "id": 2,
      "updated": null,
      "about": null,
      "education": null,
      "languages": null,
      "created": "2025-07-03T14:04:40.889584"
    },
    "experiences": [],
    "skills": [
      {
        "name": "Python",
        "id": 601
      },
      {
        "name": "JavaScript",
        "id": 181
      }
    ],
    "resume": [
      "http://localhost:8004/user/file/2b455905-2439-43c4-9bed-9ce12349ffc9.pdf"
    ],
    "llm_insights": {
      "career_stage": "mid",
      "primary_domain": "Software Engineering",
      "years_experience": 4,
      "key_strengths": [
        "Backend Development",
        "DevOps Automation",
        "Cloud Infrastructure Optimization"
      ],
      "growth_areas": [
        "Expanding AI/ML framework knowledge (specifically deep learning)",
        "Demonstrating leadership through quantified team impact beyond code contributions"
      ],
      "recommended_roles": [
        "Senior Software Engineer",
        "DevOps Engineer",
        "Technical Lead"
      ]
    }
  }
}
```

#### Job Recommendations Response (`GET /user/me/recommendations?min_match_score=40`)

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Senior Python Developer",
      "company": "TechCorp",
      "location": "Remote",
      "description": "Looking for experienced Python developer with Django/FastAPI experience",
      "salary_min": 80000.0,
      "salary_max": 120000.0,
      "job_type": "FULL_TIME",
      "skills": ["Python", "Django", "FastAPI", "PostgreSQL"],
      "match_score": 80.0,
      "skill_match_count": 4,
      "missing_skills": [],
      "source": "StackOverflow",
      "apply_url": "https://stackoverflow.com/jobs/example1"
    },
    {
      "id": 2,
      "title": "Frontend React Developer",
      "company": "StartupXYZ",
      "location": "San Francisco, CA",
      "description": "React developer needed for fast-growing startup",
      "salary_min": 70000.0,
      "salary_max": 100000.0,
      "job_type": "FULL_TIME",
      "skills": ["React", "JavaScript", "TypeScript", "CSS"],
      "match_score": 62.5,
      "skill_match_count": 3,
      "missing_skills": ["TypeScript"],
      "source": "StackOverflow",
      "apply_url": "https://stackoverflow.com/jobs/example2"
    }
  ]
}
```

#### Detailed Job Analysis Response (`GET /user/detailed_match_analysis/job/2/?include_improvement_plan=true&include_similar_items=true`)

```json
{
  "success": true,
  "data": {
    "item": {
      "id": 2,
      "title": "Credit Management",
      "company": "Kayak Tech",
      "description": "some Description\n",
      "skills": ["Credit Management"],
      "requirements": ["Credit Management"],
      "salary": 2000.0,
      "location": "Accra Ghana",
      "type": "within_city"
    },
    "match_analysis": {
      "overall_score": 4.0,
      "skill_compatibility": 0.0,
      "matched_skills": [],
      "missing_skills": ["Credit Management"],
      "skill_gap_count": 1,
      "readiness_assessment": "Limited match - Significant preparation needed"
    },
    "improvement_plan": {
      "improvement_plan": {
        "overview": "This plan focuses on acquiring fundamental knowledge and skills in Credit Management...",
        "improvement_steps": [
          {
            "step_number": 1,
            "title": "Learn Credit Management Fundamentals",
            "description": "Gain a solid understanding of core credit management concepts...",
            "estimated_duration": "4 weeks",
            "actions": [
              "Complete an introductory online course on Credit Management or Financial Analysis...",
              "Read books or articles on credit risk management..."
            ]
          }
        ],
        "estimated_time": "12 weeks",
        "resources": [
          "Investopedia (Financial Dictionary and Explanations)",
          "Coursera/edX/Udemy: Credit Management and Financial Analysis Courses"
        ],
        "notes": "This plan assumes a significant time commitment per week..."
      }
    },
    "similar_opportunities": [],
    "user_profile_summary": {
      "total_skills": 46,
      "experience_level": null,
      "profile_strength": "Strong"
    }
  }
}
```

#### YouTube Videos Response (`GET /youtube/videos/?skill_type=missing`)

```json
{
  "success": true,
  "result": {
    "requested_skills": ["TypeScript"],
    "successful_skills": [
      {
        "skill": "TypeScript",
        "level": "beginner"
      }
    ],
    "skill_type": "missing",
    "intelligent_levels": false,
    "total_videos": 5,
    "videos": [
      {
        "id": "ahCwqrYpIuM",
        "title": "TypeScript - The Basics",
        "description": "TypeScript has forever altered the lives of JavaScript developers...",
        "thumbnail": "https://i.ytimg.com/vi/ahCwqrYpIuM/hqdefault.jpg",
        "url": "https://www.youtube.com/watch?v=ahCwqrYpIuM",
        "duration": "12:01",
        "view_count": 1627138,
        "like_count": 49103,
        "published_date": "2018-11-29",
        "channel_name": "Fireship",
        "skill": "TypeScript",
        "level": "beginner"
      }
    ]
  }
}
```

#### User Preferences Response (`GET /user/preferences`)

```json
{
  "success": true,
  "data": {
    "min_match_score": 41.0,
    "preferred_job_types": ["Full-time", "Part-time", "Contract", "Internship"],
    "preferred_locations": ["Hybrid", "Remote", "Hybrid"],
    "salary_expectations": {},
    "enable_remote_jobs": true,
    "enable_semantic_matching": true,
    "auto_refresh_matches": true,
    "notification_threshold": 70.0
  }
}
```

## Development Flow

### Phase 1: Home Page Job Recommendations

#### 1.1 Update Home Page Layout

- Create a clean card-based layout for job listings
- Add badge system to distinguish:
  - **Internal jobs** (from company database)
  - **External jobs** (from job boards like StackOverflow)
- Display match score prominently on each job card

#### 1.2 Job Card Components

```
JobCard:
- Job title & company
- Location & salary range
- Match score (with progress indicator)
- Internal/External badge
- Skills matched count
- Quick apply button
```

#### 1.3 API Integration for Jobs

- Integrate `GET /user/me/recommendations?min_match_score=40`
- Parse response data array to show:
  - `title`, `company`, `location`, `description`
  - `match_score` with visual progress indicator (0-100)
  - `skill_match_count` and `missing_skills.length`
  - Badge based on `source` field (Internal if not external source like "StackOverflow")
  - Salary range from `salary_min` and `salary_max`
  - `job_type` display
  - `apply_url` for external application

### Phase 2: Skills Analysis & Recommendations

#### 2.1 Skills Cards Section

Create a dedicated section showing:

- **Matched Skills Cards**: Skills user has that match job requirements
- **Missing Skills Cards**: Skills user needs to develop
- Clean card design with skill name and proficiency level

#### 2.2 Missing Skills Learning Recommendations

When user taps on missing skills card:

- Use `GET /youtube/videos/?skill_type=missing` API
- Display videos from the `result.videos` array:
  - `thumbnail` image from YouTube
  - `title` and truncated `description`
  - `duration` and formatted `view_count`
  - `channel_name` and `published_date`
  - `skill` and `level` tags
  - Direct link using `url` field
  - Share button using `share_plus` to share video URL

#### 2.3 Skills Data Display

For each video recommendation show:

- Skill level badge from `level` field (beginner/intermediate/advanced)
- Video engagement metrics: `view_count`, `like_count`
- Published date formatting from `published_date`
- Clean, scannable UI focusing on `title`, `channel_name`, `duration`

### Phase 3: Job Detail View

#### 3.1 Detailed Job Analysis

- Use `GET /user/detailed_match_analysis/job/{id}/` API
- Parse `data.item` for job details:
  - `title`, `company`, `description`, `location`, `salary`
  - `skills` and `requirements` arrays
- Display `data.match_analysis`:
  - `overall_score` as main compatibility indicator
  - `skill_compatibility` percentage
  - `matched_skills` and `missing_skills` arrays
  - `readiness_assessment` text
  - `skill_gap_count` for quick overview

#### 3.2 Improvement Plan Section

Display the `data.improvement_plan.improvement_plan`:

- `overview` text as introduction
- `improvement_steps` array showing:
  - `step_number`, `title`, `description`
  - `estimated_duration` for each step
  - `actions` array as actionable items
- Total `estimated_time` for complete plan
- `resources` array as learning materials
- `notes` for additional context
- Share improvement plan using `share_plus`

#### 3.3 Apply Functionality

- For external jobs: Use `apply_url` from job recommendations
- For internal jobs: Internal application flow
- Display `data.user_profile_summary` showing:
  - `total_skills`, `experience_level`, `profile_strength`
- Share job details using `share_plus`

### Phase 4: User Profile & Preferences

#### 4.1 User Detail Page Updates

- Use `GET /user` API to display user information
- Show `llm_insights` in user settings page with:
  - `career_stage`, `primary_domain`, `years_experience`
  - `key_strengths` as skill tags
  - `growth_areas` as improvement suggestions
  - `recommended_roles` as career suggestions
- Skills display with "View More" toggle:
  - Show first 5 skills from `skills` array
  - "View More" button to show all skills
  - Toggle to collapse back to first 5
- Resume upload functionality
- Experience level settings
- Profile strength indicator

#### 4.2 Preferences Management

- Integrate `GET /user/preferences` to load current settings
- Use `PATCH /user/preferences` to update preferences from `data` object:
  - `min_match_score` slider (0-100)
  - `preferred_job_types` array (multi-select from available types)
  - `preferred_locations` array (multi-select locations)
  - `salary_expectations` object for salary range
  - Boolean toggles for:
    - `enable_remote_jobs`
    - `enable_semantic_matching`
    - `auto_refresh_matches`
  - `notification_threshold` slider (0-100)

#### 4.3 Settings UI Components

```
Preferences Screen matching API structure:
- Match Score Threshold: min_match_score (slider: 0-100)
- Job Types: preferred_job_types (multi-select chips)
- Locations: preferred_locations (searchable multi-select)
- Salary Range: salary_expectations (dual slider)
- Toggle switches mapping to boolean fields:
  - "Enable Remote Jobs": enable_remote_jobs
  - "Semantic Matching": enable_semantic_matching
  - "Auto Refresh": auto_refresh_matches
- Notification threshold: notification_threshold (slider: 0-100)
- Refresh Icon: Use POST /user/refresh_all_matches/ to refresh cache
```

## UI/UX Guidelines

### Design Principles

- **Clean & Modern**: Material Design 3 or Cupertino style
- **Card-based Layout**: Consistent card design across all sections
- **Progressive Disclosure**: Show essential info first, details on tap
- **Visual Hierarchy**: Clear typography and spacing
- **Color Coding**: Different colors for match scores, job types, skill levels

### Key UI Elements

- **Match Score**: Circular progress indicator with percentage
- **Badges**: Rounded badges for Internal/External job classification
- **Skill Cards**: Compact cards with clear skill names and levels
- **Video Cards**: Thumbnail, title, metadata in clean layout
- **Action Buttons**: Consistent button styling with clear CTAs

### User Experience Flow

1. **Home**: Quick job overview with match scores
2. **Skills**: Easy skill gap identification and learning paths
3. **Details**: Comprehensive job analysis with actionable insights
4. **Profile**: Simple preference and skill management

## Known Bugs to Fix

### 1. Access Token Expiry Handling

- **Issue**: When access token expires, app doesn't automatically log user out
- **Fix**: Implement token expiry detection in API calls
- **Solution**:
  - Check for 401/403 responses in all API calls
  - Clear stored authentication data
  - Navigate to login screen automatically
  - Show appropriate message to user

### 2. Internal Course Video Player Issue

- **Issue**: Video player doesn't play when clicking on internal course videos
- **Fix**: Debug video player initialization and URL handling
- **Solution**:
  - Verify video URL format and accessibility
  - Check video player widget configuration
  - Ensure proper video controls and playback initialization
  - Test with different video formats if needed

## Technical Implementation Details

### State Management

- Use appropriate state management (Provider, Riverpod, or Bloc)
- Handle loading states for all API calls
- Implement error handling and retry mechanisms

### API Integration

- Create service classes for each API endpoint
- Implement proper HTTP error handling
- Add request/response logging for debugging
- Use proper models for type safety

### Key Packages

- `http` or `dio`: API calls
- `share_plus`: Sharing functionality (already added)
- `cached_network_image`: Efficient image loading
- `url_launcher`: External link handling
- State management package of choice

## Development Priorities

### Phase 1 Priority (Core Features)

1. Job recommendations display
2. Internal/External job classification
3. Basic skills analysis
4. Job detail view with apply functionality

### Phase 2 Priority (Enhanced Features)

1. Missing skills recommendations with videos
2. Improvement plan display
3. Enhanced user profile management
4. Preferences management

### Phase 3 Priority (Polish)

1. Advanced UI animations

2. Performance optimizations

## Quality Assurance

### Testing Strategy

- Unit tests for API services
- Widget tests for key components
- Integration tests for main user flows

### Performance Considerations

- Lazy loading for job lists
- Image caching for video thumbnails
- Efficient state management
- API response caching where appropriate

## Notes for AI Agent

1. **Existing Behavior**: Keep current Flutter app behavior but integrate new APIs
2. **Comments**: Minimal comments unless necessary for complex logic
3. **Share Integration**: Use existing `share_plus` implementation pattern
4. **UI Focus**: Prioritize clean, intuitive UI/UX over complex features
5. **Data Display**: Show necessary information from rich API responses, avoid overwhelming users
6. **Error Handling**: Implement graceful error handling for all API calls
7. **Responsive Design**: Ensure app works well on different screen sizes
